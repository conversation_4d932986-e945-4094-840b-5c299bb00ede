# Ka<PERSON><PERSON> Script Backend

歌舞伎脚本分析・編集システムのバックエンド API です。

## 概要

手書き赤入れ PDF を OpenAI GPT-4o-mini（最新マルチモーダルモデル）で自動読み取りし、縦書き Word 文書へ整形する作業を大幅に効率化する Web アプリケーションのバックエンド部分です。

## 技術スタック

- **フレームワーク**: FastAPI 0.115.5
- **言語**: Python 3.9+
- **ASGI サーバー**: Uvicorn 0.34.0
- **データベース**: PostgreSQL + SQLAlchemy 2.0.36
- **認証**: Python-Jose 3.3.0 + Passlib 1.7.4
- **PDF 処理**: pdf2image 1.17.0 + Pillow 11.0.0
- **AI**: OpenAI GPT-4o-mini (Vision API)
- **Word 文書生成**: python-docx 1.1.2
- **マイグレーション**: Alembic 1.14.0
- **設定管理**: Pydantic Settings

## セットアップ

### 前提条件

- Python 3.9 以上
- PostgreSQL
- Poetry (推奨) または pip
- OpenAI API キー

### Poetry を使用したセットアップ（推奨）

```bash
# Poetryのインストール（未インストールの場合）
curl -sSL https://install.python-poetry.org | python3 -

# 依存関係のインストール
poetry install

# 仮想環境のアクティベート
poetry shell

# 環境変数の設定
cp env.example .env
# .envファイルを編集して必要な値を設定

# データベースマイグレーション
poetry run alembic upgrade head

# 開発サーバーの起動
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### pip を使用したセットアップ

```bash
# 仮想環境の作成
python -m venv venv
source venv/bin/activate  # Linux/Mac
# または
venv\Scripts\activate  # Windows

# 依存関係のインストール
pip install -r requirements.txt

# 環境変数の設定
cp env.example .env
# .envファイルを編集して必要な値を設定

# データベースマイグレーション
alembic upgrade head

# 開発サーバーの起動
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## プロジェクト構造

```
app/
├── __init__.py
├── main.py              # FastAPI アプリケーションのエントリーポイント
├── config.py            # 設定管理（Pydantic Settings）
├── database.py          # データベース設定
├── models.py            # SQLAlchemy モデル
├── schemas.py           # Pydantic スキーマ
├── auth.py              # 認証関連
├── api/                 # API ルーター
│   ├── __init__.py
│   ├── auth.py          # 認証API
│   └── routes.py        # メインAPIルート
└── services/            # ビジネスロジック
    ├── __init__.py
    ├── analysis_service.py  # GPT-4o-mini画像解析サービス
    ├── layout_service.py    # レイアウトサービス
    ├── pdf_service.py       # PDF処理サービス
    └── word_service.py      # Word文書生成サービス
```

## 環境変数

`.env` ファイルを作成して以下の環境変数を設定してください：

```env
# データベース設定
DATABASE_URL=postgresql://username:password@localhost:5432/kabuki_script

# OpenAI設定
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

# セキュリティ設定
SECRET_KEY=your_secret_key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=30

# 画像解析設定
MAX_IMAGE_SIZE_MB=20
SUPPORTED_IMAGE_FORMATS=["png", "jpg", "jpeg"]

# 解析プロンプト設定（必要に応じてカスタマイズ可能）
ANALYSIS_SYSTEM_PROMPT="あなたは歌舞伎脚本の専門家です..."
```

## 主要機能

### GPT-4o-mini 画像解析

- **手書き文字認識**: 縦書きの日本語テキストを高精度で読み取り
- **台本構造解析**: 台詞、ト書き、入り、役者名を自動分類
- **信頼度評価**: 各行の読み取り精度を 0.0-1.0 で評価
- **エラーハンドリング**: 読み取れない文字は「●」で表現

### 解析結果の形式

```json
{
  "lines": [
    {
      "line_id": "line-1",
      "line_type": "normal",
      "actor": "助六",
      "text": "いざ鎌倉",
      "confidence": 0.95,
      "has_unreadable_chars": false
    }
  ],
  "overall_confidence": 0.9
}
```

## API エンドポイント

### 認証

- `POST /api/auth/register` - ユーザー登録
- `POST /api/auth/login` - ログイン
- `POST /api/auth/refresh` - トークンリフレッシュ

### PDF 処理

- `POST /api/pdf/upload` - PDF アップロード
- `POST /api/pdf/analyze` - PDF 分析（GPT-4o-mini 使用）
- `POST /api/pdf/convert` - PDF 変換

### 脚本編集

- `POST /api/script/apply-layout` - レイアウト適用
- `POST /api/script/generate-word` - Word 文書生成

## 開発

### 開発サーバーの起動

```bash
# Poetry を使用
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# または pip を使用
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

API ドキュメントは `http://localhost:8000/docs` で確認できます。

### 画像解析のテスト

```bash
# テストスクリプトの実行
python test_analysis.py
```

テスト用の画像ファイル（test_image.png）を用意して実行してください。

### コード品質

```bash
# コードフォーマット
poetry run black .
poetry run isort .

# リンター
poetry run flake8 .

# 型チェック
poetry run mypy .
```

### テスト

```bash
# テストの実行
poetry run pytest

# カバレッジ付きテスト
poetry run pytest --cov=app
```

## データベースマイグレーション

```bash
# マイグレーションファイルの作成
poetry run alembic revision --autogenerate -m "description"

# マイグレーションの実行
poetry run alembic upgrade head

# マイグレーションのロールバック
poetry run alembic downgrade -1
```

詳細は `MIGRATION.md` を参照してください。

## 設定管理

### 設定ファイル（config.py）

- **OpenAI 設定**: モデル、トークン数、温度などの調整
- **画像解析設定**: 対応フォーマット、最大サイズ
- **解析プロンプト**: カスタマイズ可能なシステムプロンプト
- **セキュリティ設定**: JWT、データベース接続

### 環境変数の優先順位

1. 環境変数
2. .env ファイル
3. デフォルト値

## デプロイ

### Docker を使用したデプロイ

```bash
# Dockerイメージのビルド
docker build -t kabuki-script-backend .

# コンテナの実行
docker run -p 8000:8000 kabuki-script-backend
```

### 本番環境での起動

```bash
# 本番用サーバー起動
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## トラブルシューティング

### よくある問題

1. **OpenAI API エラー**

   - API キーが正しく設定されているか確認
   - クレジット残高を確認
   - レート制限に注意

2. **画像解析エラー**

   - 画像フォーマットが対応しているか確認
   - 画像サイズが制限内か確認
   - 画像の品質を確認

3. **データベース接続エラー**

   - DATABASE_URL が正しく設定されているか確認
   - PostgreSQL が起動しているか確認

## ライセンス

MIT License
