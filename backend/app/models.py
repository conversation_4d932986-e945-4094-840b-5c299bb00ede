from sqlalchemy import (
    Column,
    Integer,
    String,
    Float,
    <PERSON>olean,
    DateTime,
    Text,
    ForeignKey,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    # リレーション
    pdf_documents = relationship("PDFDocument", back_populates="user")


class PDFDocument(Base):
    __tablename__ = "pdf_documents"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    filename = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    upload_time = Column(DateTime(timezone=True), server_default=func.now())

    # リレーション
    user = relationship("User", back_populates="pdf_documents")
    analysis_results = relationship("AnalysisResult", back_populates="document")


class AnalysisResult(Base):
    __tablename__ = "analysis_results"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("pdf_documents.id"), nullable=False)
    confidence = Column(Float, nullable=False)
    processing_time = Column(Float, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # リレーション
    document = relationship("PDFDocument", back_populates="analysis_results")
    paragraphs = relationship("Paragraph", back_populates="analysis_result")


class Paragraph(Base):
    __tablename__ = "paragraphs"

    id = Column(Integer, primary_key=True, index=True)
    analysis_result_id = Column(
        Integer, ForeignKey("analysis_results.id"), nullable=False
    )
    type = Column(String, nullable=False)  # lines, lyrics, directions
    header = Column(String, nullable=True)
    content = Column(Text, nullable=False)

    # リレーション
    analysis_result = relationship("AnalysisResult", back_populates="paragraphs")


# class ScriptLine(Base):
#     __tablename__ = "script_lines"

#     id = Column(Integer, primary_key=True, index=True)
#     analysis_result_id = Column(Integer, ForeignKey("analysis_results.id"), nullable=False)
#     line_id = Column(String, nullable=False)
#     line_type = Column(String, nullable=False)  # normal, iori, tobogaki, actor, empty
#     actor = Column(String, nullable=True)
#     text = Column(Text, nullable=False)
#     x = Column(Float, nullable=True)
#     y = Column(Float, nullable=True)
#     confidence = Column(Float, nullable=True)
#     has_unreadable_chars = Column(Boolean, default=False)
#     original_text = Column(Text, nullable=True)

#     # リレーション
#     analysis_result = relationship("AnalysisResult", back_populates="script_lines")
