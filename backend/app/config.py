import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings

# プロンプトをファイルから読み込む
with open("app/static/prompt.md", "r") as f:
    prompt = f.read()


class Settings(BaseSettings):
    # OpenAI設定
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4o-mini", env="OPENAI_MODEL")
    openai_max_tokens: int = Field(default=4000, env="OPENAI_MAX_TOKENS")
    openai_temperature: float = Field(default=0.1, env="OPENAI_TEMPERATURE")

    # 画像解析設定
    max_image_size_mb: int = Field(default=20, env="MAX_IMAGE_SIZE_MB")
    supported_image_formats: list = Field(
        default=["jpg", "jpeg"], env="SUPPORTED_IMAGE_FORMATS"
    )

    # 解析プロンプト設定
    instructions: str = (
        "優秀な画像認識アシスタントとして、与えられた画像からテキストを抽出し、構造化されたJSONデータに変換しなさい。"
    )
    image_analysis_system_prompt: str = prompt
    analysis_system_prompt: str = """あなたは歌舞伎脚本の専門家です。歌舞伎脚本のテキストを解析し、必ず以下の形式でJSONレスポンスを返してください。

重要: 必ず有効なJSON形式で返してください。テキストや説明文は含めず、JSONオブジェクトのみを返してください。

解析ルール:
1. 縦書きの日本語テキストを正確に読み取る
2. 各行の種類を判定する:
   - normal: 通常の台詞
   - iori: 入り（背景説明）
   - tobogaki: ト書き（動作指示）
   - actor: 役者名
   - empty: 空行
3. 役者名がある場合は抽出する
4. 読み取れない文字は「●」で表現し、has_unreadable_charsをtrueにする
5. 信頼度（confidence）を0.0-1.0で評価する

レスポンス形式（必ずこの形式で返してください）:
{
  "lines": [
    {
      "line_id": "line-1",
      "line_type": "normal",
      "actor": "助六",
      "text": "いざ鎌倉",
      "confidence": 0.95,
      "has_unreadable_chars": false
    },
    {
      "line_id": "line-2",
      "line_type": "normal",
      "actor": "助六",
      "text": "花の吉原で",
      "confidence": 0.85,
      "has_unreadable_chars": false
    },
    {
      "line_id": "line-3",
      "line_type": "tobogaki",
      "text": "花道より登場",
      "confidence": 0.8,
      "has_unreadable_chars": false
    }
  ],
  "overall_confidence": 0.87
}

注意: 
- 必ず有効なJSON形式で返してください。テキストや説明文は含めないでください。
- title、author、contentなどの不要なフィールドは含めないでください。
- 各行には必ずline_id、line_type、text、confidence、has_unreadable_charsフィールドを含めてください。
- actorフィールドは役者名がある場合のみ含めてください。"""

    # データベース設定
    database_url: str = Field(..., env="DATABASE_URL")

    # JWT設定（環境変数から取得、デフォルト値なし）
    secret_key: str = Field(..., env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(
        default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES"
    )
    refresh_token_expire_days: int = Field(default=30, env="REFRESH_TOKEN_EXPIRE_DAYS")
    model_config = {"env_file": ".env", "case_sensitive": False}


# グローバル設定インスタンス
settings = Settings()
