from pydantic import BaseModel, EmailStr, field_validator
from typing import List, Optional, Literal
from datetime import datetime
import re


# User schemas
class UserBase(BaseModel):
    email: str
    username: str

    @field_validator("email")
    @classmethod
    def validate_email(cls, v):
        # 基本的なメールアドレス形式チェック
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(pattern, v):
            raise ValueError("Invalid email format")
        return v


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    email: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None


class User(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    username: Optional[str] = None


class LoginRequest(BaseModel):
    username: str
    password: str


# 基本スキーマ
class Paragraph(BaseModel):
    type: Literal["lines", "lyrics", "directions"]
    header: str
    content: str


class Script(BaseModel):
    paragraphs: List[Paragraph]
    confidence: float


# PDFページデータ
class PDFPageData(BaseModel):
    page_number: int
    image_url: str
    width: int
    height: int


# 解析結果
class AnalysisResultBase(BaseModel):
    confidence: float
    processing_time: float


class AnalysisResultCreate(AnalysisResultBase):
    document_id: int


class AnalysisResult(AnalysisResultBase):
    id: int
    document_id: int
    created_at: datetime
    paragraphs: List[Paragraph] = []

    model_config = {"from_attributes": True}


# Vision API解析結果（レスポンス用）
class VisionAnalysisResult(BaseModel):
    paragraphs: List[Paragraph]
    confidence: float
    processing_time: float


# PDFドキュメント
class PDFDocumentBase(BaseModel):
    filename: str
    original_filename: str
    file_size: int


class PDFDocumentCreate(PDFDocumentBase):
    pass


class PDFDocument(PDFDocumentBase):
    id: int
    user_id: int
    upload_time: datetime
    analysis_results: List[AnalysisResult] = []

    model_config = {"from_attributes": True}


# # レイアウトルール
# class LayoutRule(BaseModel):
#     line_type: str
#     indent_chars: int
#     alignment: str
#     prefix: Optional[str] = None


# Word生成オプション
class WordGenerationOptions(BaseModel):
    characters_per_line: int = 24
    vertical_writing: bool = True
    font_size: int = 12
    font_family: str = "游明朝"


# # API レスポンス
# class LayoutApplyResponse(BaseModel):
#     lines: List[ScriptLineBase]
