from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List
import io

from app.database import get_db
from app.models import User
from app.auth import get_current_active_user
from app.schemas import (
    VisionAnalysisResult,
    PDFPageData,
    WordGenerationOptions,
    PDFDocument,
    AnalysisResult,
)
from app.services.pdf_service import PDFService
from app.services.analysis_service import AnalysisService

# from app.services.word_service import WordService

router = APIRouter()


@router.get("/documents", response_model=List[PDFDocument])
async def get_user_documents(
    current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)
):
    """ユーザーのPDFドキュメント一覧を取得"""
    pdf_service = PDFService(db)
    return pdf_service.get_user_documents(current_user.id)


@router.get("/analysis-results", response_model=List[AnalysisResult])
async def get_user_analysis_results(
    current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)
):
    """ユーザーの解析結果一覧を取得"""
    analysis_service = AnalysisService(db)
    return analysis_service.get_user_analysis_results(current_user.id)


@router.post("/pdf-to-images", response_model=List[PDFPageData])
async def pdf_to_images(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """PDFファイルを画像に変換"""
    if not file.filename.lower().endswith(".pdf"):
        raise HTTPException(status_code=400, detail="PDFファイルが必要です")

    pdf_service = PDFService(db)
    try:
        content = await file.read()
        pages = await pdf_service.convert_to_images(
            content, file.filename, current_user.id
        )
        return pages
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF変換に失敗しました: {str(e)}")


@router.post("/analyze", response_model=VisionAnalysisResult)
async def analyze_pdf(
    file: UploadFile = File(None),
    image_data: str = Form(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """PDFファイルまたは画像データを解析して台本データを抽出"""
    analysis_service = AnalysisService(db)

    try:
        import logging

        logger = logging.getLogger(__name__)
        logger.info(
            f"解析リクエスト受信: file={file is not None}, image_data={image_data is not None}"
        )

        if image_data:
            # 画像データの場合（Base64形式）
            logger.info(
                f"画像データ受信: 長さ={len(image_data)}, プレフィックス={image_data[:50]}..."
            )

            # data:image/jpeg;base64, の部分を除去
            if image_data.startswith("data:image"):
                image_data = image_data.split(",")[1]
                logger.info(f"Base64部分抽出: 長さ={len(image_data)}")

            result = await analysis_service.analyze_image_directly(
                image_data, "selected_image.jpg", current_user.id
            )
        elif file:
            # PDFファイルの場合
            logger.info(f"PDFファイル処理: {file.filename}")
            if not file.filename.lower().endswith(".pdf"):
                raise HTTPException(status_code=400, detail="PDFファイルが必要です")
            content = await file.read()
            result = await analysis_service.analyze_document(
                content, file.filename, current_user.id
            )
        else:
            raise HTTPException(
                status_code=400, detail="PDFファイルまたは画像データが必要です"
            )

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"解析に失敗しました: {str(e)}")


# @router.post("/apply-layout", response_model=LayoutApplyResponse)
# async def apply_layout(
#     lines: List[ScriptLineBase],
#     current_user: User = Depends(get_current_active_user),
#     db: Session = Depends(get_db)
# ):
#     """台本データにレイアウトルールを適用"""
#     layout_service = LayoutService(db)
#     try:
#         formatted_lines = layout_service.apply_layout_rules(lines)
#         return LayoutApplyResponse(lines=formatted_lines)
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"レイアウト適用に失敗しました: {str(e)}")


# @router.post("/generate-word")
# async def generate_word(
#     lines: List[ScriptLineBase],
#     options: WordGenerationOptions = None,
#     current_user: User = Depends(get_current_active_user),
#     db: Session = Depends(get_db),
# ):
#     """台本データからWord文書を生成"""
#     word_service = WordService(db)
#     try:
#         if options is None:
#             options = WordGenerationOptions()

#         doc_buffer = word_service.generate_document(lines, options)

#         return StreamingResponse(
#             io.BytesIO(doc_buffer),
#             media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
#             headers={"Content-Disposition": "attachment; filename=kabuki-script.docx"},
#         )
#     except Exception as e:
#         raise HTTPException(
#             status_code=500, detail=f"Word文書の生成に失敗しました: {str(e)}"
#         )
