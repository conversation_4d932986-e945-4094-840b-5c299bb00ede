from sqlalchemy.orm import Session
from typing import List
import base64
import uuid
import os
from io import BytesIO
from pdf2image import convert_from_bytes
from PIL import Image

from app.schemas import PDFPageData
from app.models import PDFDocument
from app.database import get_db

class PDFService:
    def __init__(self, db: Session):
        self.db = db
    
    async def convert_to_images(self, content: bytes, filename: str, user_id: int) -> List[PDFPageData]:
        """PDFファイルを画像に変換"""
        try:
            # PDFドキュメントをデータベースに保存
            pdf_doc = PDFDocument(
                user_id=user_id,
                filename=f"{uuid.uuid4()}_{filename}",
                original_filename=filename,
                file_size=len(content)
            )
            self.db.add(pdf_doc)
            self.db.commit()
            self.db.refresh(pdf_doc)
            
            # PDFを画像に変換
            images = convert_from_bytes(
                content,
                dpi=150,  # 解像度を150DPIに設定
                fmt='JPEG',  # JPEG形式で出力
                size=(None, None)  # 元のサイズを維持
            )
            
            pages = []
            for i, image in enumerate(images, 1):
                # PIL画像をBytesIOに変換
                img_buffer = BytesIO()
                image.save(img_buffer, format='JPEG', quality=90)
                img_buffer.seek(0)
                
                # Base64エンコード
                img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
                
                # 画像のサイズを取得
                width, height = image.size
                
                pages.append(PDFPageData(
                    page_number=i,
                    image_url=f"data:image/jpeg;base64,{img_base64}",
                    width=width,
                    height=height
                ))
            
            return pages
            
        except Exception as e:
            raise Exception(f"PDF変換エラー: {str(e)}")
    
    def get_document_by_id(self, doc_id: int, user_id: int) -> PDFDocument:
        """IDでPDFドキュメントを取得（ユーザー固有）"""
        return self.db.query(PDFDocument).filter(
            PDFDocument.id == doc_id,
            PDFDocument.user_id == user_id
        ).first()
    
    def get_user_documents(self, user_id: int) -> List[PDFDocument]:
        """ユーザーのPDFドキュメント一覧を取得"""
        return self.db.query(PDFDocument).filter(
            PDFDocument.user_id == user_id
        ).order_by(PDFDocument.upload_time.desc()).all() 