# from sqlalchemy.orm import Session
# from typing import List, Dict

# from app.schemas import ScriptLineBase, LayoutRule

# class LayoutService:
#     def __init__(self, db: Session):
#         self.db = db
#         self.layout_rules = self._get_layout_rules()
#         self.characters_per_line = 24

#     def _get_layout_rules(self) -> Dict[str, LayoutRule]:
#         """歌舞伎台本のレイアウトルールを取得"""
#         return {
#             'normal': LayoutRule(
#                 line_type='normal',
#                 indent_chars=0,
#                 alignment='left',
#             ),
#             'iori': LayoutRule(
#                 line_type='iori',
#                 indent_chars=2,
#                 alignment='left',
#             ),
#             'tobogaki': LayoutRule(
#                 line_type='tobogaki',
#                 indent_chars=4,
#                 alignment='left',
#                 prefix='ト',
#             ),
#             'actor': LayoutRule(
#                 line_type='actor',
#                 indent_chars=1,
#                 alignment='right',
#             ),
#             'empty': LayoutRule(
#                 line_type='empty',
#                 indent_chars=0,
#                 alignment='left',
#             ),
#         }

#     def apply_layout_rules(self, lines: List[ScriptLineBase]) -> List[ScriptLineBase]:
#         """各行にレイアウトルールを適用"""
#         formatted_lines = []

#         for line in lines:
#             formatted_line = self._apply_layout_to_line(line)
#             formatted_lines.append(formatted_line)

#         # 役者名とセリフの関係を整理
#         organized_lines = self._organize_actor_lines(formatted_lines)

#         return organized_lines

#     def _apply_layout_to_line(self, line: ScriptLineBase) -> ScriptLineBase:
#         """単一行にレイアウトルールを適用"""
#         rule = self.layout_rules.get(line.line_type)
#         if not rule:
#             return line

#         formatted_text = line.text

#         # プレフィックスを追加
#         if rule.prefix and not formatted_text.startswith(rule.prefix):
#             formatted_text = rule.prefix + formatted_text

#         # インデントを適用
#         indent = '　' * rule.indent_chars

#         # 24文字制限を適用
#         formatted_text = self._apply_line_length_limit(formatted_text, indent)

#         # 新しいScriptLineBaseオブジェクトを作成
#         return ScriptLineBase(
#             line_id=line.line_id,
#             line_type=line.line_type,
#             actor=line.actor,
#             text=formatted_text,
#             x=line.x,
#             y=line.y,
#             confidence=line.confidence,
#             has_unreadable_chars=line.has_unreadable_chars,
#             original_text=line.original_text
#         )

#     def _apply_line_length_limit(self, text: str, indent: str) -> str:
#         """24文字制限を適用"""
#         lines = []
#         current_line = indent + text

#         while len(current_line) > self.characters_per_line:
#             # 24文字で切り取り
#             cut_point = self.characters_per_line

#             # 可能であれば句読点や区切りの良い場所で切る
#             punctuation = ['。', '、', '！', '？', '」', '』', '）']
#             for i in range(self.characters_per_line - 1, self.characters_per_line - 5, -1):
#                 if i < len(current_line) and current_line[i] in punctuation:
#                     cut_point = i + 1
#                     break

#             lines.append(current_line[:cut_point])
#             current_line = indent + current_line[cut_point:]

#         if current_line.strip():
#             lines.append(current_line)

#         return '\n'.join(lines)

#     def _organize_actor_lines(self, lines: List[ScriptLineBase]) -> List[ScriptLineBase]:
#         """役者名とセリフの関係を整理"""
#         organized_lines = []

#         for i, line in enumerate(lines):
#             if line.line_type == 'actor':
#                 organized_lines.append(line)

#                 # 次の行がセリフの場合、役者名を設定
#                 if i + 1 < len(lines):
#                     next_line = lines[i + 1]
#                     if next_line.line_type == 'normal' and not next_line.actor:
#                         # 新しいオブジェクトを作成して役者名を設定
#                         updated_line = ScriptLineBase(
#                             line_id=next_line.line_id,
#                             line_type=next_line.line_type,
#                             actor=line.text.strip(),
#                             text=next_line.text,
#                             x=next_line.x,
#                             y=next_line.y,
#                             confidence=next_line.confidence,
#                             has_unreadable_chars=next_line.has_unreadable_chars,
#                             original_text=next_line.original_text
#                         )
#                         lines[i + 1] = updated_line
#             else:
#                 organized_lines.append(line)

#         return organized_lines
