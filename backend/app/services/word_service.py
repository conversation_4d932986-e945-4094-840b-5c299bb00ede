# from sqlalchemy.orm import Session
# from typing import List
# from docx import Document
# from docx.shared import Inches, Pt
# from docx.enum.text import WD_ALIGN_PARAGRAPH
# from docx.enum.style import WD_STYLE_TYPE
# from io import BytesIO

# from app.schemas import WordGenerationOptions

# class WordService:
#     def __init__(self, db: Session):
#         self.db = db

#     def generate_document(self, lines: List[ScriptLineBase], options: WordGenerationOptions) -> bytes:
#         """台本データからWord文書を生成"""
#         try:
#             # 新しいWord文書を作成
#             doc = Document()

#             # ページ設定
#             section = doc.sections[0]
#             section.page_height = Inches(11.69)  # A4縦
#             section.page_width = Inches(8.27)
#             section.top_margin = Inches(1)
#             section.bottom_margin = Inches(1)
#             section.left_margin = Inches(1)
#             section.right_margin = Inches(1)

#             # 各行を文書に追加
#             for line in lines:
#                 self._add_line_to_document(doc, line, options)

#             # バイナリデータとして返す
#             doc_buffer = BytesIO()
#             doc.save(doc_buffer)
#             doc_buffer.seek(0)

#             return doc_buffer.getvalue()

#         except Exception as e:
#             raise Exception(f"Word文書生成エラー: {str(e)}")

#     def _add_line_to_document(self, doc: Document, line: ScriptLineBase, options: WordGenerationOptions):
#         """単一行を文書に追加"""
#         # 空行の場合
#         if line.line_type == 'empty' or not line.text.strip():
#             paragraph = doc.add_paragraph('')
#             paragraph.space_after = Pt(6)
#             return

#         # 役者名の場合
#         if line.line_type == 'actor':
#             paragraph = doc.add_paragraph()
#             run = paragraph.add_run(line.text)
#             run.bold = True
#             run.font.size = Pt(options.font_size)
#             run.font.name = options.font_family
#             paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
#             paragraph.left_indent = Inches(self._get_indent_inches(line.line_type))
#             paragraph.space_after = Pt(3)
#             return

#         # ト書きの場合
#         if line.line_type == 'tobogaki':
#             paragraph = doc.add_paragraph()
#             run = paragraph.add_run(line.text)
#             run.italic = True
#             run.font.size = Pt(options.font_size - 1)
#             run.font.name = options.font_family
#             paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
#             paragraph.left_indent = Inches(self._get_indent_inches(line.line_type))
#             paragraph.space_after = Pt(3)
#             return

#         # いおり点段落の場合
#         if line.line_type == 'iori':
#             paragraph = doc.add_paragraph()
#             run = paragraph.add_run(line.text)
#             run.font.size = Pt(options.font_size)
#             run.font.name = options.font_family
#             paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
#             paragraph.left_indent = Inches(self._get_indent_inches(line.line_type))
#             paragraph.space_after = Pt(3)
#             return

#         # 通常のテキスト
#         paragraph = doc.add_paragraph()
#         run = paragraph.add_run(line.text)
#         run.font.size = Pt(options.font_size)
#         run.font.name = options.font_family
#         paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
#         paragraph.left_indent = Inches(self._get_indent_inches(line.line_type))
#         paragraph.space_after = Pt(3)

#     def _get_indent_inches(self, line_type: str) -> float:
#         """行タイプに応じたインデント（インチ）を取得"""
#         indent_map = {
#             'iori': 0.5,      # 2文字分
#             'tobogaki': 1.0,  # 4文字分
#             'actor': 0.25,    # 1文字分
#             'normal': 0.0,
#             'empty': 0.0,
#         }
#         return indent_map.get(line_type, 0.0)
