from sqlalchemy.orm import Session
from typing import List, Dict, Any
import time
import json
import base64
import io
import os
from datetime import datetime
from PIL import Image
from openai import OpenAI
import logging

from app.schemas import VisionAnalysisResult, Script, Paragraph
from app.models import PDFDocument, AnalysisResult, Paragraph as ParagraphModel
from app.services.pdf_service import PDFService
from app.config import settings

# ログ設定
logger = logging.getLogger(__name__)


class AnalysisService:
    def __init__(self, db: Session):
        self.db = db
        self.openai_client = OpenAI(api_key=settings.openai_api_key)

    async def analyze_image_directly(
        self, image_base64: str, filename: str, user_id: int
    ) -> VisionAnalysisResult:
        """Base64画像データを直接解析して台本データを抽出"""
        start_time = time.time()

        try:
            logger.info(f"画像解析開始: {filename}, Base64長: {len(image_base64)}")

            # 画像データを直接解析
            analysis_result = await self._analyze_image_with_openai_directly(
                image_base64
            )

            processing_time = time.time() - start_time

            # 解析結果をデータベースに保存
            pdf_doc = PDFDocument(
                user_id=user_id,
                filename=filename,
                original_filename=filename,
                file_size=len(image_base64),
            )
            self.db.add(pdf_doc)
            self.db.commit()
            self.db.refresh(pdf_doc)

            # 解析結果を保存
            db_analysis_result = AnalysisResult(
                document_id=pdf_doc.id,
                confidence=analysis_result.confidence,
                processing_time=processing_time,
            )
            self.db.add(db_analysis_result)
            self.db.commit()
            self.db.refresh(db_analysis_result)

            # 段落を保存
            for paragraph in analysis_result.paragraphs:
                self.db.add(
                    ParagraphModel(
                        analysis_result_id=db_analysis_result.id,
                        type=paragraph.type,
                        header=paragraph.header,
                        content=paragraph.content,
                    )
                )
            self.db.commit()

            # レスポンス用のオブジェクトを作成
            result = VisionAnalysisResult(
                paragraphs=analysis_result.paragraphs,
                confidence=analysis_result.confidence,
                processing_time=processing_time,
            )

            logger.info(
                f"画像解析完了: {len(analysis_result.paragraphs)}段落, 処理時間: {processing_time:.2f}秒"
            )
            return result

        except Exception as e:
            logger.error(f"解析エラー: {str(e)}")
            raise Exception(f"解析エラー: {str(e)}")

    async def _analyze_image_with_openai_directly(self, image_base64: str) -> Script:
        """Base64画像データを直接OpenAI GPT-4o-miniで解析（段落ごとに処理）"""
        try:
            # デバッグ用: Base64画像データをJPEGファイルとして保存
            self._save_debug_image(image_base64)

            # 画像から文字を読み取り（）
            logger.info(f"画像解析開始: {image_base64[:100]}")
            response = self.openai_client.responses.parse(
                model=settings.openai_model,
                instructions=settings.instructions,
                input=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "input_text",
                                "text": settings.image_analysis_system_prompt,
                            },
                            {
                                "type": "input_image",  # type: ignore
                                "image_url": f"data:image/jpeg;base64,{image_base64}",
                            },
                        ],
                    },
                ],
                temperature=settings.openai_temperature,
                max_output_tokens=settings.openai_max_tokens,
                text_format=Script,
            )
            logger.info(f"... {response}")
            return (
                response.output_parsed
                if response.output_parsed
                else self._get_fallback_result()
            )

        except Exception as e:
            logger.error(f"OpenAI API エラー: {str(e)}")
            # フォールバック: モックデータを返す
            return self._get_fallback_result()

    def _get_fallback_result(self) -> Script:
        """フォールバック用のモックデータ"""
        return Script(
            paragraphs=[
                Paragraph(
                    type="lines",
                    header="簑作",
                    content="我れ民間に育ち、人に面を見知られぬを幸いに、花作りとなって入り込みしは、幼君の御身の上に、もし、過ちやあらんかと、余所ながら守護する某、それと悟って抱えしや、はて。",
                ),
                Paragraph(
                    type="lyrics",
                    header="",
                    content="合点の行かぬと差俯き　思案に塞がる一間には館の娘八重垣姫　云い号ある勝頼の切腹ありしその日より　一間処に引篭り　床に絵姿掛けまくも　御経読誦のりんの音",
                ),
                Paragraph(
                    type="directions",
                    header="ト、",
                    content="上手ミスを上げる八重垣姫床の間の掛け軸を掛け経机を置き読誦している",
                ),
                Paragraph(
                    type="lines",
                    header="濡衣",
                    content="広い世界に誰れあって、お前の忌日命日を、弔う人も情けなや、父御の悪事も露知らず、お果てなされたお心を思い出す程おいとしい、さぞや未来は迷うてござろう、女房の濡衣が、心ばかりのこの手向け、千部万部の御経ぞと、思うて成仏して下さんせ、南無阿弥陀仏/く。",
                ),
            ],
            confidence=0.8,
        )

    def _save_debug_image(self, image_base64: str) -> None:
        """デバッグ用にBase64画像データをJPEGファイルとして保存"""
        try:
            # デバッグディレクトリの作成
            debug_dir = "debug_images"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)
                logger.info(f"デバッグディレクトリを作成: {debug_dir}")

            # タイムスタンプ付きファイル名の生成
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[
                :-3
            ]  # ミリ秒まで含む
            filename = f"debug_image_{timestamp}.jpg"
            filepath = os.path.join(debug_dir, filename)

            # Base64データをデコードして画像として保存
            image_data = base64.b64decode(image_base64)
            with open(filepath, "wb") as f:
                f.write(image_data)

            # 画像サイズ情報を取得
            try:
                image = Image.open(io.BytesIO(image_data))
                width, height = image.size
                file_size = len(image_data)
                logger.info(
                    f"デバッグ画像を保存: {filepath} (サイズ: {width}x{height}, ファイルサイズ: {file_size} bytes)"
                )
            except Exception as img_error:
                logger.warning(f"画像サイズ取得エラー: {str(img_error)}")
                logger.info(f"デバッグ画像を保存: {filepath}")

        except Exception as e:
            logger.error(f"デバッグ画像保存エラー: {str(e)}")

    def get_user_analysis_results(self, user_id: int) -> List[AnalysisResult]:
        """ユーザーの解析結果一覧を取得"""
        return (
            self.db.query(AnalysisResult)
            .join(PDFDocument)
            .filter(PDFDocument.user_id == user_id)
            .order_by(AnalysisResult.created_at.desc())
            .all()
        )
