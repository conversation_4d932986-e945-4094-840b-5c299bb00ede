from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import logging
import os

from app.api.routes import router
from app.api.auth import router as auth_router
from app.database import engine
from app.models import Base
from app.config import settings

# ログ設定
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("app.log")
    ]
)

logger = logging.getLogger(__name__)

# データベーステーブル作成
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Kabuki Script API",
    description="手書き赤入れPDFを自動読み取りし、縦書きWord文書へ整形するAPI",
    version="1.0.0"
)

# CORS設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://frontend:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静的ファイル配信
os.makedirs("app/static", exist_ok=True)
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# APIルーター
app.include_router(auth_router, prefix="/api/auth", tags=["authentication"])
app.include_router(router, prefix="/api", tags=["api"])

@app.get("/")
async def root():
    return {"message": "Kabuki Script API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.on_event("startup")
async def startup_event():
    logger.info("Kabuki Script API が起動しました")
    logger.info(f"OpenAI Model: {settings.openai_model}")
    logger.info(f"OpenAI API Key: {settings.openai_api_key[:10]}...")
    logger.info(f"Database URL: {settings.database_url}")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Kabuki Script API がシャットダウンしました") 