FROM python:3.11-slim

WORKDIR /app

# システムの依存関係をインストール
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    poppler-utils \
    && rm -rf /var/lib/apt/lists/*

# Poetryをインストール
RUN curl -sSL https://install.python-poetry.org | python3 -

# Poetryのパスを環境変数に追加
ENV PATH="/root/.local/bin:$PATH"

# Poetryの設定（仮想環境を作成しない）
RUN poetry config virtualenvs.create false

# pyproject.tomlとREADME.mdをコピー
COPY pyproject.toml README.md ./

# 依存関係をインストール（lockファイルを自動生成）
COPY . .
RUN rm -rf poetry.lock
RUN poetry install --only=main --no-interaction --no-ansi

# ポート8000を公開
EXPOSE 8000

# Uvicornサーバーを起動
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"] 