#!/usr/bin/env python3
"""
サンプルユーザーを作成するスクリプト
"""

import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

# プロジェクトルートを追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models import Base, User
from app.auth import get_password_hash
from app.config import settings

def create_sample_user():
    """サンプルユーザーを作成する"""
    
    # データベース接続
    engine = create_engine(settings.database_url)
    Base.metadata.create_all(bind=engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    db = SessionLocal()
    
    try:
        # 既存のユーザーをチェック
        existing_user = db.query(User).filter(User.username == "admin").first()
        if existing_user:
            print("❌ ユーザー 'admin' は既に存在します")
            return False
        
        # サンプルユーザーを作成
        sample_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            is_active=True
        )
        
        db.add(sample_user)
        db.commit()
        db.refresh(sample_user)
        
        print("✅ サンプルユーザーが正常に作成されました:")
        print(f"   ユーザー名: admin")
        print(f"   パスワード: admin123")
        print(f"   メール: <EMAIL>")
        print(f"   ユーザーID: {sample_user.id}")
        
        return True
        
    except IntegrityError as e:
        db.rollback()
        print(f"❌ データベースエラー: {e}")
        return False
    except Exception as e:
        db.rollback()
        print(f"❌ 予期しないエラー: {e}")
        return False
    finally:
        db.close()

def main():
    """メイン関数"""
    print("🎭 歌舞伎台本自動成形システム - サンプルユーザー作成")
    print("=" * 50)
    
    success = create_sample_user()
    
    if success:
        print("\n🚀 フロントエンドでこのアカウントを使用してログインできます:")
        print("   http://localhost:3000")
    else:
        print("\n❌ サンプルユーザーの作成に失敗しました")
        sys.exit(1)

if __name__ == "__main__":
    main() 