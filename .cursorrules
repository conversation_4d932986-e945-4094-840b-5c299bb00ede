# Ka<PERSON><PERSON> - <PERSON>ursor AI Assistant Configuration

## プロジェクト概要
歌舞伎脚本分析・編集システム - 手書き赤入れPDFをOpenAI GPT-4o-miniで自動読み取りし、縦書きWord文書へ整形するWebアプリケーション

## 技術スタック
- **Frontend**: Next.js 15.3.3, React 19.1.0, TypeScript 5.8.3, Tailwind CSS 3.4.17
- **Backend**: FastAPI 0.115.5, Python 3.9+, SQLAlchemy 2.0.36, PostgreSQL
- **AI/ML**: OpenAI GPT-4o-mini Vision API, pdf2image, Pillow
- **Document Processing**: python-docx, PDF処理
- **Authentication**: JWT (python-jose, passlib)
- **Infrastructure**: Docker Compose (開発環境専用), Poetry (Python依存関係管理)

## 開発ガイドライン

### コードスタイル
- **Python**: Black (line-length=88), isort, flake8, mypy (strict mode)
- **TypeScript/JavaScript**: ESLint with Next.js config
- **React**: Functional components with hooks, TypeScript strict mode
- **FastAPI**: Pydantic v2 schemas, async/await patterns

### アーキテクチャ原則
- **Frontend**: Next.js App Router, コンポーネントベース設計
- **Backend**: FastAPI with service layer pattern, SQLAlchemy ORM
- **Database**: PostgreSQL with Alembic migrations
- **API**: RESTful design with JWT authentication

### ファイル命名規則
- **Python**: snake_case (models.py, schemas.py, auth.py)
- **TypeScript**: camelCase (PDFUploader.tsx, ScriptEditor.tsx)
- **Components**: PascalCase (PDFViewer.tsx)
- **API Routes**: kebab-case (/api/pdf-to-images)

## Docker Compose管理

### 重要なルール
- **Docker Composeファイルは1つのみ**: `docker-compose.yml`のみを使用
- **開発環境専用**: 本番環境は別途用意するため、開発環境専用の設定
- **複数ファイル禁止**: `docker-compose.override.yml`, `docker-compose.prod.yml`などの追加ファイルは作成しない
- **ホットリロード対応**: 開発効率のため、バックエンド・フロントエンド共にホットリロード有効

### Docker Compose設定内容
- **バックエンド**: FastAPI + ホットリロード (`uvicorn app.main:app --reload`)
- **フロントエンド**: Next.js + ホットリロード (`npm run dev`)
- **データベース**: PostgreSQL (開発用)
- **ボリュームマウント**: ソースコードのホットリロード対応
- **環境変数**: 開発用デフォルト値設定済み

## 主要機能ドメイン

### PDF処理
- PDFアップロード・変換 (pdf2image)
- 画像処理 (Pillow)
- OpenAI GPT-4o-mini Vision API統合

### 脚本分析・編集
- 手書き文字認識（GPT-4o-mini）
- 台本データ抽出
- レイアウト適用
- Word文書生成 (python-docx)

### 認証・セキュリティ
- JWT認証
- パスワードハッシュ化 (bcrypt)
- ユーザー管理

## 開発ワークフロー

### Docker Compose開発（推奨）
```bash
# 環境変数設定
export OPENAI_API_KEY="your-openai-api-key-here"
export SECRET_KEY="your-secret-key-here"

# 開発環境起動
docker-compose up -d

# ログ確認
docker-compose logs -f backend
docker-compose logs -f frontend

# コンテナ再起動
docker-compose restart backend
docker-compose restart frontend

# 環境変数確認
docker-compose exec backend env | grep -E "(OPENAI|SECRET|DATABASE)"
```

### バックエンド開発（ローカル）
```bash
cd backend
poetry install
poetry run uvicorn app.main:app --reload
```

### フロントエンド開発（ローカル）
```bash
cd frontend
npm install
npm run dev
```

### データベース
```bash
# Docker環境の場合
docker-compose exec backend alembic upgrade head

# ローカル環境の場合
cd backend
poetry run alembic upgrade head
```

## CursorでのDocker Compose実行

### 許可されるコマンド
- `docker-compose up -d`: 開発環境起動
- `docker-compose down`: コンテナ停止
- `docker-compose restart [service]`: サービス再起動
- `docker-compose logs -f [service]`: ログ確認
- `docker-compose exec [service] [command]`: コンテナ内コマンド実行
- `docker-compose ps`: コンテナ状態確認

### 禁止される操作
- 複数のDocker Composeファイル作成
- 本番環境用の設定ファイル作成
- 既存の`docker-compose.yml`の大幅な変更（開発環境以外の設定追加）

## コード生成ガイドライン

### FastAPI エンドポイント作成時
- Pydantic v2スキーマを使用
- 適切なHTTPステータスコードを設定
- エラーハンドリングを含める
- JWT認証が必要な場合は@Depends(get_current_user)を使用

### React コンポーネント作成時
- TypeScript strict mode準拠
- 適切な型定義を提供
- Tailwind CSSクラスを使用
- エラーバウンダリを考慮

### データベースモデル作成時
- SQLAlchemy 2.0スタイル
- 適切なリレーションシップ定義
- Alembicマイグレーション生成

### Docker Compose設定変更時
- 開発環境専用の設定のみ
- ホットリロード機能の維持
- 環境変数の適切な管理

## 品質保証
- **Python**: pytest, mypy, black, isort, flake8
- **TypeScript**: ESLint, TypeScript strict mode
- **API**: FastAPI自動生成ドキュメント (/docs)
- **Testing**: ユニットテスト、統合テスト
- **Docker**: 開発環境での動作確認

## 環境変数管理
- `.env`ファイル使用 (python-dotenv)
- 機密情報は環境変数で管理
- Docker Compose環境変数設定
- 開発用デフォルト値の提供

## デプロイメント
- Docker Compose for development only
- Production: 別途本番環境を用意
- Database: PostgreSQL with persistent volumes

## 注意事項
- OpenAI GPT-4o-mini API使用時は適切なエラーハンドリング
- PDF処理は重い処理のため非同期処理を推奨
- ファイルアップロード時のセキュリティ考慮
- 縦書きWord文書生成時の日本語フォント設定
- Docker Composeファイルは開発環境専用、本番環境では使用しない

## 参考リソース
- FastAPI Documentation: https://fastapi.tiangolo.com/
- Next.js Documentation: https://nextjs.org/docs
- OpenAI API Documentation: https://platform.openai.com/docs
- SQLAlchemy 2.0: https://docs.sqlalchemy.org/en/20/
- Docker Compose Documentation: https://docs.docker.com/compose/ 