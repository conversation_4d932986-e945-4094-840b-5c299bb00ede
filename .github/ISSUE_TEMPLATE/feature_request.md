---
name: 機能リクエスト
about: このプロジェクトのアイデアを提案してください
title: '[FEATURE] '
labels: ['type: feature']
assignees: ''
---

## 機能リクエストの概要
<!-- 提案する機能の簡潔な説明を記入してください -->

## 問題の説明
<!-- この機能が必要な理由や解決したい問題を記入してください -->

## 提案する解決策
<!-- どのような機能を追加したいかを記入してください -->

## 代替案
<!-- 他に考えられる解決策があれば記入してください -->

## 追加情報
<!-- 機能リクエストに関連するその他の情報やコンテキストを追加してください -->

## スクリーンショット
<!-- 該当する場合は、モックアップやスクリーンショットを追加してください -->

## 実装の複雑さ
<!-- 実装の複雑さを評価してください -->
- [ ] 簡単（数時間程度）
- [ ] 中程度（数日程度）
- [ ] 複雑（数週間程度）
- [ ] 非常に複雑（数ヶ月程度）

## 優先度
<!-- この機能の優先度を評価してください -->
- [ ] 低（あれば便利）
- [ ] 中（重要）
- [ ] 高（非常に重要）
- [ ] 緊急（必須）

## チェックリスト
- [ ] 既存のIssueで同様の機能が提案されていないか確認しました
- [ ] 機能の必要性を明確に説明しました
- [ ] 実装の複雑さを評価しました
- [ ] 優先度を設定しました 