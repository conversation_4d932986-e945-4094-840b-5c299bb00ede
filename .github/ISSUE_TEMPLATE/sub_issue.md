---
name: サブタスク
about: 大きなissueを小さなタスクに分割する際に使用してください
title: '[SUB] '
labels: ['type: subtask']
assignees: ''
---

## サブタスクの概要
<!-- このサブタスクで実装する機能や修正の簡潔な説明を記入してください -->

## 親Issue
<!-- このサブタスクが属する親issueへのリンクを記入してください -->
**親Issue**: #[issue番号]

## 目的
<!-- このサブタスクを実装する目的や理由を記入してください -->

## 実装内容
<!-- 具体的に何を実装するかを詳細に記入してください -->

## 技術的な詳細
<!-- 実装に必要な技術的な詳細や考慮事項を記入してください -->

### 影響範囲
<!-- この実装が影響するファイルやコンポーネントを記入してください -->
- [ ] フロントエンド
- [ ] バックエンド
- [ ] データベース
- [ ] API
- [ ] その他

### 依存関係
<!-- このサブタスクが依存する他のサブタスクや外部ライブラリを記入してください -->
**前提条件**:
- [ ] サブタスク #[番号] の完了
- [ ] 外部ライブラリの更新
- [ ] その他

## 実装手順
<!-- 実装の手順を段階的に記入してください -->
1. [ ] 手順1
2. [ ] 手順2
3. [ ] 手順3
4. [ ] テスト
5. [ ] レビュー

## 完了条件
<!-- このサブタスクが完了したとみなす条件を記入してください -->
- [ ] 機能の実装完了
- [ ] 単体テストの作成・実行
- [ ] 統合テストの実行
- [ ] コードレビューの完了
- [ ] ドキュメントの更新

## 見積もり
<!-- 実装にかかる時間の見積もりを記入してください -->
**予想工数**: [例: 2-3時間]

## リスク・注意点
<!-- 実装時のリスクや注意点があれば記入してください -->

## 追加情報
<!-- その他の関連情報やコンテキストを追加してください -->

## チェックリスト
- [ ] 親issueとの関連性を明確にしました
- [ ] 実装内容を詳細に記述しました
- [ ] 依存関係を確認しました
- [ ] 完了条件を明確にしました
- [ ] 見積もりを設定しました 