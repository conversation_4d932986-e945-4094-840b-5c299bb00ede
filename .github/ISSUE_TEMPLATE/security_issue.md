---
name: セキュリティ問題報告
about: セキュリティの問題を報告してください
title: '[SECURITY] '
labels: ['type: security']
assignees: ''
---

## セキュリティ問題の概要
<!-- 発見したセキュリティ問題の簡潔な説明を記入してください -->

## 問題の詳細
<!-- セキュリティ問題の詳細な説明を記入してください -->

## 影響範囲
<!-- このセキュリティ問題の影響範囲を記入してください -->
- [ ] 認証・認可
- [ ] データ漏洩
- [ ] 入力検証
- [ ] 暗号化
- [ ] セッション管理
- [ ] その他: _________

## 脆弱性の種類
<!-- 該当する脆弱性の種類を選択してください -->
- [ ] SQLインジェクション
- [ ] XSS（クロスサイトスクリプティング）
- [ ] CSRF（クロスサイトリクエストフォージェリ）
- [ ] 認証バイパス
- [ ] 権限昇格
- [ ] 情報漏洩
- [ ] その他: _________

## 再現手順
<!-- セキュリティ問題を再現するための手順を記入してください -->
1. '....' に移動
2. '....' を実行
3. '....' を確認
4. 問題が発生

## 期待される動作
<!-- 正常に動作する場合の期待される動作を記入してください -->

## 実際の動作
<!-- 実際に発生している動作を記入してください -->

## セキュリティレベル
<!-- このセキュリティ問題の深刻度を評価してください -->
- [ ] 低（情報漏洩の可能性）
- [ ] 中（機能の悪用可能性）
- [ ] 高（システムへの重大な影響）
- [ ] 緊急（即座に対応が必要）

## 提案する修正方法
<!-- このセキュリティ問題の修正方法を提案してください -->

## 追加情報
<!-- セキュリティ問題に関連するその他の情報やコンテキストを追加してください -->

## チェックリスト
- [ ] 既存のIssueで同様の問題が報告されていないか確認しました
- [ ] セキュリティ問題の詳細を明確に説明しました
- [ ] 影響範囲を特定しました
- [ ] 再現手順を詳細に記述しました
- [ ] セキュリティレベルを評価しました
- [ ] 修正方法を提案しました