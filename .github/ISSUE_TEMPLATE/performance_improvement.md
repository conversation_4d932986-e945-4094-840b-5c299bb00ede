---
name: パフォーマンス改善要求
about: パフォーマンスの改善を提案してください
title: '[PERFORMANCE] '
labels: ['type: performance']
assignees: ''
---

## パフォーマンス改善の概要
<!-- 改善したいパフォーマンスの問題の簡潔な説明を記入してください -->

## 現在の問題
<!-- 現在のパフォーマンスの問題点を記入してください -->

## 影響を受ける機能
<!-- パフォーマンスの問題が影響する機能を記入してください -->

## 現在のパフォーマンス指標
<!-- 現在のパフォーマンス指標があれば記入してください -->
- レスポンス時間: [例: 3秒]
- メモリ使用量: [例: 512MB]
- CPU使用率: [例: 80%]
- その他: _________

## 目標とするパフォーマンス指標
<!-- 改善後の目標とするパフォーマンス指標を記入してください -->
- レスポンス時間: [例: 1秒以下]
- メモリ使用量: [例: 256MB以下]
- CPU使用率: [例: 50%以下]
- その他: _________

## 提案する改善方法
<!-- どのような改善方法を提案するかを記入してください -->

## 対象となるファイル・モジュール
<!-- 改善対象となるファイルやモジュールを記入してください -->

## 実装の複雑さ
<!-- 改善の複雑さを評価してください -->
- [ ] 簡単（数時間程度）
- [ ] 中程度（数日程度）
- [ ] 複雑（数週間程度）
- [ ] 非常に複雑（数ヶ月程度）

## 優先度
<!-- このパフォーマンス改善の優先度を評価してください -->
- [ ] 低（あれば便利）
- [ ] 中（重要）
- [ ] 高（非常に重要）
- [ ] 緊急（必須）

## 追加情報
<!-- パフォーマンス改善に関連するその他の情報やコンテキストを追加してください -->

## チェックリスト
- [ ] 既存のIssueで同様の改善が提案されていないか確認しました
- [ ] パフォーマンスの問題を明確に説明しました
- [ ] 現在のパフォーマンス指標を測定しました
- [ ] 目標とするパフォーマンス指標を設定しました
- [ ] 改善方法を具体的に提案しました
- [ ] 実装の複雑さを評価しました
- [ ] 優先度を設定しました 