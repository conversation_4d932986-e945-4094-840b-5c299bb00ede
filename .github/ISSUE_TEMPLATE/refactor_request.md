---
name: リファクタリング要求
about: コードのリファクタリングを提案してください
title: '[REFACTOR] '
labels: ['type: refactor']
assignees: ''
---

## リファクタリングの概要
<!-- リファクタリングしたいコードの簡潔な説明を記入してください -->

## 現在の問題
<!-- 現在のコードの問題点や改善が必要な理由を記入してください -->

## 提案する改善内容
<!-- どのようなリファクタリングをしたいかを記入してください -->

## 対象となるファイル・モジュール
<!-- リファクタリング対象となるファイルやモジュールを記入してください -->

## 期待される効果
<!-- リファクタリングにより期待される効果を記入してください -->
- [ ] コードの可読性向上
- [ ] 保守性の向上
- [ ] パフォーマンスの改善
- [ ] バグの削減
- [ ] テストの改善
- [ ] その他: _________

## 実装の複雑さ
<!-- リファクタリングの複雑さを評価してください -->
- [ ] 簡単（数時間程度）
- [ ] 中程度（数日程度）
- [ ] 複雑（数週間程度）
- [ ] 非常に複雑（数ヶ月程度）

## 優先度
<!-- このリファクタリングの優先度を評価してください -->
- [ ] 低（あれば便利）
- [ ] 中（重要）
- [ ] 高（非常に重要）
- [ ] 緊急（必須）

## 追加情報
<!-- リファクタリングに関連するその他の情報やコンテキストを追加してください -->

## チェックリスト
- [ ] 既存のIssueで同様のリファクタリングが提案されていないか確認しました
- [ ] リファクタリングの必要性を明確に説明しました
- [ ] 対象となるファイル・モジュールを特定しました
- [ ] 期待される効果を明確にしました
- [ ] 実装の複雑さを評価しました
- [ ] 優先度を設定しました 