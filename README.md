# Kabuki Script

手書き赤入れPDFを OpenAI GPT-4o-mini（最新マルチモーダルモデル）で自動読み取りし、縦書きWord文書へ整形する作業を大幅に効率化するWebアプリケーションです。

## アーキテクチャ

このアプリケーションは以下の構成で動作します：

- **Frontend**: Next.js SPA (Static Site Generation)
- **Backend**: FastAPI (Python) + GPT-4o-mini Vision API
- **Database**: PostgreSQL with SQLAlchemy
- **Container**: Docker Compose (開発環境専用)

## 🚀 クイックスタート

### 必要な環境

- Docker
- Docker Compose
- OpenAI API キー

### 1. リポジトリのクローン

```bash
git clone https://github.com/BOC-Ltd/kabuki-script.git
cd kabuki-script
```

### 2. 環境変数の設定

#### .envファイルの作成

プロジェクトルートに`.env`ファイルを作成：

```bash
# .env の内容（キーなど）は適宜修正してください。
cp .env.example .env
```

### 3. アプリケーションの起動

```bash
# 開発環境で起動（ホットリロード有効）
docker-compose up -d

# ログの確認
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 4. 環境変数の確認

```bash
# バックエンドコンテナの環境変数を確認
docker-compose exec backend env | grep -E "(OPENAI|SECRET|DATABASE)"

# フロントエンドコンテナの環境変数を確認
docker-compose exec frontend env | grep -E "(NEXT_PUBLIC|NODE_ENV)"
```

### 5. ユーザー登録・ログイン

アプリケーション起動後、以下のAPIエンドポイントを使用してユーザー登録・ログインを行います：

```bash
# ユーザー登録
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "username": "testuser", "password": "password123"}'

# ログイン
curl -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=password123"
```

## 📚 API エンドポイント

### Authentication - http://localhost:8000/api/auth

- `POST /api/auth/register` - ユーザー登録
- `POST /api/auth/login` - ユーザーログイン
- `GET /api/auth/me` - 現在のユーザー情報取得

### Backend (FastAPI) - http://localhost:8000

- `GET /api/documents` - ユーザーのPDFドキュメント一覧取得
- `GET /api/analysis-results` - ユーザーの解析結果一覧取得
- `POST /api/pdf-to-images` - PDFファイルを画像に変換
- `POST /api/analyze` - PDFファイルを解析して台本データを抽出（GPT-4o-mini使用）
- `POST /api/apply-layout` - 台本データにレイアウトルールを適用
- `POST /api/generate-word` - 台本データからWord文書を生成
- `GET /docs` - API仕様書 (Swagger UI)

**注意**: 上記のAPIエンドポイントは全てJWT認証が必要です。AuthorizationヘッダーにBearerトークンを含めてリクエストしてください。

### Frontend (Next.js SPA) - http://localhost:3000

静的サイトとして配信され、バックエンドAPIと通信します。

## 🛠 開発

### バックエンド開発

コンテナの中に入りたい場合

```bash
docker compose exec backend bash
```

### フロントエンド開発

コンテナの中に入りたい場合

```bash
docker compose exec frontend bash
```

(初回) node_modulesの反映

```bash
docker compose run --rm frontend npm install
```

## 📁 プロジェクト構造

```
kabuki-script/
├── backend/                 # FastAPI バックエンド
│   ├── app/
│   │   ├── api/            # API ルート
│   │   ├── services/       # ビジネスロジック（GPT-4o-mini画像解析）
│   │   ├── models.py       # SQLAlchemy モデル
│   │   ├── schemas.py      # Pydantic スキーマ
│   │   ├── config.py       # 設定管理（Pydantic Settings）
│   │   └── main.py         # FastAPI アプリケーション
│   ├── pyproject.toml      # Poetry設定ファイル
│   ├── poetry.lock         # Poetry依存関係ロックファイル
│   └── Dockerfile          # バックエンド用 Docker
├── frontend/               # Next.js フロントエンド
│   ├── src/
│   │   ├── app/           # App Router
│   │   ├── components/    # React コンポーネント
│   │   ├── lib/          # ユーティリティ
│   │   └── types/        # TypeScript 型定義
│   ├── package.json      # Node.js 依存関係
│   ├── next.config.js    # Next.js 設定
│   └── Dockerfile        # フロントエンド用 Docker
├── docker-compose.yml     # 開発環境用Docker Compose設定
├── DOCKER_ENV_SETUP.md    # Docker環境変数設定ガイド
└── README.md
```

## 🔧 主な機能

1. **PDFアップロード**: 手書き赤入れPDFファイルのアップロード
2. **自動解析**: OpenAI GPT-4o-mini Vision APIを使用した手書き文字認識
3. **台本編集**: 解析結果の手動編集・修正
4. **レイアウト適用**: 歌舞伎台本の伝統的なレイアウトルール適用
5. **Word出力**: 縦書きWord文書の生成・ダウンロード

## 🤝 Contributing

0. 対応するタスクのIssueを確認、もしくはIssueを作成する
1. developブランチから、機能ブランチを作成 (`git checkout -b feature/amazing-feature`)
2. 変更をコミット (`git commit -m 'Add some amazing feature'`)
3. プッシュ (`git push origin feature/amazing-feature`)
4. プルリクエストを作成
