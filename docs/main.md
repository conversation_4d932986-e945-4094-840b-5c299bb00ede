## 🎭 概要

手書き赤入れPDFを OpenAI GPT-4o-mini（最新マルチモーダルモデル）で自動読み取りし、縦書きWord文書へ整形する作業を大幅に効率化するWebアプリケーションです。


## 🚀 主な機能

- **PDF自動解析**: OpenAI GPT-4o-mini Vision APIを使用した手書き文字認識
- **リアルタイム編集**: 分割画面でのPDF表示と編集グリッド
- **自動レイアウト**: 歌舞伎台本の書式ルール自動適用
- **Word文書生成**: 縦書き対応のdocx形式出力
- **SharePoint連携**: 自動アップロード機能

## 🛠️ 技術スタック

- **Frontend**: Next.js 15 + TypeScript + Tailwind CSS
- **Backend**: Next.js API Routes
- **Vision API**: OpenAI GPT-4o-mini Vision API
- **Deployment**: Vercel


1. **PDF アップロード**: ドラッグ&ドロップでPDFファイルをアップロード
2. **自動解析**: 「解析開始」ボタンでVision API処理を開始
   1. 役者名、役者名の下にセリフ、改行の記号を含めるデータ形式で出力
3. **表示**: 
   1. 画面左半分でアップロードしたPDFを表示
   2. 画面右半分でvisionの認識結果を確認・修正
      - グリッドとして、上段に役者名、下段にセリフ
      - 未読文字（●）のフィルタリング
      - 行タイプの変更（ト書き、いおり点など）
      - リアルタイム編集

4. **レイアウト適用**: 歌舞伎台本の書式ルールを自動適用

5. **Word生成**: 縦書きWord文書として出力
   - ダウンロード機能

## 📊 パフォーマンス目標

- **対応ブラウザ**: Chrome/Edge

## 🎨 歌舞伎台本書式ルール

| 区分 | ルール |
|------|--------|
| いおり点段落 | 行頭から2文字下げ |
| ト書き | 行頭に「ト」、4文字下げ |
| 人名 | セリフ行の直上に配置、右寄せ（1文字下げ） |
| 空行 | 場面転換時に1行追加 |
| 1行文字数 | 24文字固定 |

---

# 歌舞伎台本 自動成形システム 要件定義書

## 1. 目的 / 背景

* **目的**: 手書き赤入れ PDF を OpenAI o4 Vision で自動読み取りし、縦書き Word へ整形する作業を大幅に効率化する。
* **背景**:

  * 現在 10 ページで約 3 時間、月末 50–100 ページで徹夜（最大 10 時間）。
  * 編集担当 4–5 名が翌朝までに納品必須。

## 2. ステークホルダー

| 役割       | 人数    | 期待・責務                 |
| -------- | ----- | --------------------- |
| 台本整理担当者  | 4–5 名 | PDF アップロード・校正・Word 出力 |
| 部署トップ    | 1 名   | 社内最終承認                |
| 親会社（納品先） | 受入担当  | 納品物受領・検収              |

## 3. 機能一覧 (User Stories)

1. **PDF アップロード** – 編集者は赤入れ PDF をドラッグ&ドロップで登録したい。
2. **ビジョン文字起こし** – システムは PDF を 1 行単位で解析しテキスト＋座標を保持する。
3. **グリッドビュー編集** – 編集者は画面左に PDF、右に解析結果を表形式で修正したい。
4. **レイアウト自動適用** – 編集者はルールをワンクリックで適用したい。
5. **縦書き Word 出力** – 編集者はボタン 1 つで最終 .docx を生成・DL したい。
6. **未読文字マーキング** – システムは 4oが読めない文字を "●" で自動マークし、要確認フィルタを提供する。

## 4. 非機能要件

* **性能**: 100 ページ（約 20,000 行）を 1 時間以内に解析完了。
* **可用性**: 月 1–2 回のピークに対応、SLA 99.5%。
* **精度**: 自動レイアウト適用後の手修正が 5 % 以内。
* **セキュリティ**: PDF/Word は社内ネットワーク内のみ保存。
* **操作性**: Chrome/Edge 対応、ドラッグ&ドロップ。
* **ログ**: アップロード・編集・出力操作を記録。

## 5. 制約

* OCR 不使用（OpenAI o4 Vision 限定）。
* Word 縦書き・**1 行 24 文字固定**。
* Office365 互換の .docx。
* 現行 RPA 連携なし。

## 6. レイアウトルール一覧

| 区分     | ルール                          |
| ------ | ---------------------------- |
| いおり点段落 | 行頭から **2 文字下げ**              |
| ト書き    | 行頭に「ト」、**4 文字下げ**            |
| 人名     | セリフ行の直上に配置、右寄せ（人名自身は 1 文字下げ） |
| 空行     | 場面転換時に 1 行追加                 |
| 1 行文字数 | **24 文字固定**                  |
