services:
  # PostgreSQL Database
  db:
    image: postgres:17
    container_name: kabuki-db
    environment:
      POSTGRES_DB: kabuki_script
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - kabuki-network
    restart: unless-stopped

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: kabuki-backend
    # 開発時のボリュームマウント（ホットリロード対応）
    volumes:
      - ./backend:/app
      - ./debug_images:/app/debug_images
    # 開発時のコマンド（ホットリロード有効）
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    # 開発時の環境変数
    environment:
      # データベース設定（Docker環境用）
      DATABASE_URL: **************************************/kabuki_script
      # 開発用の設定
      DEBUG: "true"
      LOG_LEVEL: "DEBUG"
      # OpenAI設定（環境変数から取得、デフォルト値付き）
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      OPENAI_MODEL: ${OPENAI_MODEL:-gpt-4o-mini}
      OPENAI_MAX_TOKENS: ${OPENAI_MAX_TOKENS:-4000}
      OPENAI_TEMPERATURE: ${OPENAI_TEMPERATURE:-0.1}
      # JWT設定（環境変数から取得）
      SECRET_KEY: ${SECRET_KEY:-1SYMWcn5rFtQbKOAvylkGui9yEO3-k5MTI_NbDIyaK4}
      ALGORITHM: ${ALGORITHM:-HS256}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      REFRESH_TOKEN_EXPIRE_DAYS: ${REFRESH_TOKEN_EXPIRE_DAYS:-30}
      # 画像解析設定
      MAX_IMAGE_SIZE_MB: ${MAX_IMAGE_SIZE_MB:-20}
      SUPPORTED_IMAGE_FORMATS: ${SUPPORTED_IMAGE_FORMATS:-["png", "jpg", "jpeg"]}
      # 解析プロンプト設定
      ANALYSIS_SYSTEM_PROMPT: ${ANALYSIS_SYSTEM_PROMPT:-"あなたは歌舞伎脚本の専門家です。手書きの歌舞伎脚本の画像を解析し、以下の形式でJSONレスポンスを返してください。"}
    ports:
      - "8000:8000"
    depends_on:
      - db
    networks:
      - kabuki-network
    restart: unless-stopped

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: kabuki-frontend
    # 開発時のボリュームマウント（ホットリロード対応）
    volumes:
      - ./frontend:/app
      - /app/node_modules
    # 開発時のコマンド（ホットリロード有効）
    command: npm run dev
    # 開発時の環境変数
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - kabuki-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  kabuki-network:
    driver: bridge
