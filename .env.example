# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/kabuki_script

# OpenAI API
OPENAI_API_KEY=your-key
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=16384
OPENAI_TEMPERATURE=0.0

# JWT Secret Key (本番環境では強力な秘密鍵を使用してください)
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=30

# Image Analysis Settings
MAX_IMAGE_SIZE_MB=100
SUPPORTED_IMAGE_FORMATS=["png", "jpg", "jpeg"]

NEXT_PUBLIC_APP_URL=http://localhost:3000