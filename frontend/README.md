# Ka<PERSON><PERSON> Script Frontend

歌舞伎脚本分析・編集システムのフロントエンドアプリケーションです。

## 概要

手書き赤入れPDFを OpenAI GPT-4o（最新マルチモーダルモデル）で自動読み取りし、縦書きWord文書へ整形する作業を大幅に効率化するWebアプリケーションのフロントエンド部分です。

## 技術スタック

- **フレームワーク**: Next.js 15.3.3
- **言語**: TypeScript 5.8.3
- **UIライブラリ**: React 19.1.0
- **スタイリング**: Tailwind CSS 3.4.17
- **PDF表示**: react-pdf 9.2.1

## セットアップ

### 前提条件

- Node.js (v18以上推奨)
- npm または yarn

### インストール

```bash
# 依存関係のインストール
npm install

# 開発サーバーの起動
npm run dev
```

### 利用可能なスクリプト

```bash
# 開発サーバーの起動
npm run dev

# プロダクションビルド
npm run build

# プロダクションサーバーの起動
npm run start

# 静的エクスポート
npm run export

# リンターの実行
npm run lint
```

## プロジェクト構造

```
src/
├── app/                 # Next.js App Router
│   ├── api/            # API ルート
│   │   ├── analyze/    # 分析API
│   │   ├── apply-layout/ # レイアウト適用API
│   │   ├── generate-word/ # Word生成API
│   │   └── pdf-to-images/ # PDF変換API
│   ├── globals.css     # グローバルスタイル
│   ├── layout.tsx      # ルートレイアウト
│   └── page.tsx        # ホームページ
├── components/         # React コンポーネント
│   ├── PDFUploader.tsx # PDFアップローダー
│   ├── PDFViewer.tsx   # PDFビューアー
│   └── ScriptEditor.tsx # 脚本エディター
├── lib/               # ユーティリティライブラリ
│   └── api.ts         # API クライアント
└── types/             # TypeScript型定義
    └── index.ts       # 共通型定義
```

## 開発

### 開発サーバーの起動

```bash
npm run dev
```

アプリケーションは `http://localhost:3000` で起動します。

### コード品質

```bash
# リンターの実行
npm run lint
```

## デプロイ

### Docker を使用したデプロイ

```bash
# Dockerイメージのビルド
docker build -t kabuki-script-frontend .

# コンテナの実行
docker run -p 3000:3000 kabuki-script-frontend
```

### 静的エクスポート

```bash
npm run export
```

## 環境変数

`.env.local` ファイルを作成して以下の環境変数を設定してください：

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## ライセンス

ISC License 