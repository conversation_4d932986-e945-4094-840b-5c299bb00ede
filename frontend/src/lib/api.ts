import { VisionAnalysisResult, Paragraph } from "@/types";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

class APIClient {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem("token");
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  async pdfToImages(file: File) {
    const formData = new FormData();
    formData.append("file", file);

    const response = await fetch(`${this.baseURL}/api/pdf-to-images`, {
      method: "POST",
      headers: {
        ...this.getAuthHeaders(),
      },
      body: formData,
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("認証が必要です。ログインしてください。");
      }
      throw new Error(`PDF変換エラー: ${response.statusText}`);
    }

    return response.json();
  }

  async analyzePDF(file: File): Promise<VisionAnalysisResult> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await fetch(`${this.baseURL}/api/analyze`, {
      method: "POST",
      headers: {
        ...this.getAuthHeaders(),
      },
      body: formData,
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("認証が必要です。ログインしてください。");
      }
      throw new Error(`PDF解析エラー: ${response.statusText}`);
    }

    return response.json();
  }

  async analyzeImage(imageData: string): Promise<VisionAnalysisResult> {
    console.log("画像データ送信:", {
      length: imageData.length,
      prefix: imageData.substring(0, 50),
      isDataUrl: imageData.startsWith("data:image"),
    });

    const formData = new FormData();
    formData.append("image_data", imageData);

    const response = await fetch(`${this.baseURL}/api/analyze`, {
      method: "POST",
      headers: {
        ...this.getAuthHeaders(),
      },
      body: formData,
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("認証が必要です。ログインしてください。");
      }
      throw new Error(`画像解析エラー: ${response.statusText}`);
    }

    return response.json();
  }

  async applyLayout(lines: any[]) {
    const response = await fetch(`${this.baseURL}/api/apply-layout`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...this.getAuthHeaders(),
      },
      body: JSON.stringify(lines),
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("認証が必要です。ログインしてください。");
      }
      throw new Error("レイアウト適用に失敗しました");
    }

    return response.json();
  }

  async generateWord(lines: Paragraph[]): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/api/generate-word`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...this.getAuthHeaders(),
      },
      body: JSON.stringify({ lines }),
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error("認証が必要です。ログインしてください。");
      }
      throw new Error(`Word生成エラー: ${response.statusText}`);
    }

    return response.blob();
  }
}

export const apiClient = new APIClient();
