'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { PDFPageData } from '@/types'

interface ImageViewerProps {
  pages: PDFPageData[]
  onImageSelect: (imageData: string, cropData?: { x: number; y: number; width: number; height: number }) => void
  onCurrentPageChange?: (pageData: PDFPageData) => void
}

interface Selection {
  startX: number
  startY: number
  endX: number
  endY: number
  isSelecting: boolean
}

export default function ImageViewer({ pages, onImageSelect, onCurrentPageChange }: ImageViewerProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [selection, setSelection] = useState<Selection>({
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
    isSelecting: false
  })
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)

  const currentPageData = pages.find(page => page.page_number === currentPage)
  
  // 現在のページが変わったときに親コンポーネントに通知
  useEffect(() => {
    if (currentPageData && onCurrentPageChange) {
      onCurrentPageChange(currentPageData)
    }
  }, [currentPageData, onCurrentPageChange])

  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return
    
    const rect = canvasRef.current.getBoundingClientRect()
    const canvas = canvasRef.current
    
    // キャンバスの表示サイズと実際のサイズの比率を計算
    const scaleX = canvas.width / rect.width
    const scaleY = canvas.height / rect.height
    
    // マウス座標をキャンバスの実際の座標系に変換
    const x = (e.clientX - rect.left) * scaleX
    const y = (e.clientY - rect.top) * scaleY
    
    console.log('マウスダウン座標変換:', {
      clientX: e.clientX,
      clientY: e.clientY,
      rectLeft: rect.left,
      rectTop: rect.top,
      scaleX,
      scaleY,
      convertedX: x,
      convertedY: y,
      canvasWidth: canvas.width,
      canvasHeight: canvas.height,
      rectWidth: rect.width,
      rectHeight: rect.height
    })
    
    setSelection({
      startX: x,
      startY: y,
      endX: x,
      endY: y,
      isSelecting: true
    })
  }, [])

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!selection.isSelecting || !canvasRef.current) return
    
    const rect = canvasRef.current.getBoundingClientRect()
    const canvas = canvasRef.current
    
    // キャンバスの表示サイズと実際のサイズの比率を計算
    const scaleX = canvas.width / rect.width
    const scaleY = canvas.height / rect.height
    
    // マウス座標をキャンバスの実際の座標系に変換
    const x = (e.clientX - rect.left) * scaleX
    const y = (e.clientY - rect.top) * scaleY
    
    console.log('マウス移動座標変換:', {
      clientX: e.clientX,
      clientY: e.clientY,
      rectLeft: rect.left,
      rectTop: rect.top,
      scaleX,
      scaleY,
      convertedX: x,
      convertedY: y
    })
    
    setSelection((prev: Selection) => ({
      ...prev,
      endX: x,
      endY: y
    }))
  }, [selection.isSelecting])

  const handleMouseUp = useCallback(() => {
    if (!selection.isSelecting || !canvasRef.current || !imageRef.current) return
    
    setSelection((prev: Selection) => ({ ...prev, isSelecting: false }))
    
    // キャンバスを取得
    const canvas = canvasRef.current
    
    // 選択範囲を計算
    const x = Math.min(selection.startX, selection.endX)
    const y = Math.min(selection.startY, selection.endY)
    const width = Math.abs(selection.endX - selection.startX)
    const height = Math.abs(selection.endY - selection.startY)
    
    // 最小選択サイズをチェック（実際のキャンバス座標系に基づく）
    const minSize = Math.min(canvas.width, canvas.height) * 0.1 // 画像の10%を最小サイズとする
    
    console.log('選択範囲計算:', {
      startX: selection.startX,
      startY: selection.startY,
      endX: selection.endX,
      endY: selection.endY,
      calculatedX: x,
      calculatedY: y,
      calculatedWidth: width,
      calculatedHeight: height,
      minSize
    })
    if (width < minSize || height < minSize) {
      // 選択範囲が小さすぎる場合は画像全体を使用
      console.log('選択範囲が小さすぎるため、画像全体を使用します')
      const fullImageData = canvas.toDataURL('image/jpeg', 0.9)
      
      console.log('画像全体データ:', {
        length: fullImageData.length,
        prefix: fullImageData.substring(0, 50),
        width: canvas.width,
        height: canvas.height
      })
      
      onImageSelect(fullImageData)
      return
    }
    
    // 選択された画像部分を切り出し
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const croppedCanvas = document.createElement('canvas')
    const croppedCtx = croppedCanvas.getContext('2d')
    if (!croppedCtx) return
    
    croppedCanvas.width = width
    croppedCanvas.height = height
    
    croppedCtx.drawImage(
      canvas,
      x, y, width, height,
      0, 0, width, height
    )
    
    // 切り出した画像をBase64に変換
    const croppedImageData = croppedCanvas.toDataURL('image/jpeg', 0.9)
    
    console.log('切り出し画像データ:', {
      length: croppedImageData.length,
      prefix: croppedImageData.substring(0, 50),
      width,
      height
    })
    
    // 親コンポーネントに通知
    onImageSelect(croppedImageData, { x, y, width, height })
  }, [selection, onImageSelect])

  const drawSelection = useCallback(() => {
    if (!canvasRef.current || !imageRef.current) return
    
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    // 画像を描画
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.drawImage(imageRef.current, 0, 0, canvas.width, canvas.height)
    
    // 選択範囲を描画
    if (selection.isSelecting || (selection.startX !== selection.endX && selection.startY !== selection.endY)) {
      const x = Math.min(selection.startX, selection.endX)
      const y = Math.min(selection.startY, selection.endY)
      const width = Math.abs(selection.endX - selection.startX)
      const height = Math.abs(selection.endY - selection.startY)
      
      // 半透明のオーバーレイ
      ctx.fillStyle = 'rgba(0, 123, 255, 0.3)'
      ctx.fillRect(x, y, width, height)
      
      // 境界線
      ctx.strokeStyle = '#007bff'
      ctx.lineWidth = 2
      ctx.strokeRect(x, y, width, height)
    }
  }, [selection])

  // 画像が読み込まれたときにキャンバスを描画
  const handleImageLoad = useCallback(() => {
    if (!canvasRef.current || !imageRef.current) return
    
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    // キャンバスサイズを画像に合わせる
    canvas.width = imageRef.current.naturalWidth
    canvas.height = imageRef.current.naturalHeight
    
    drawSelection()
  }, [drawSelection])

  // 選択状態が変わったときに再描画
  useEffect(() => {
    drawSelection()
  }, [drawSelection])

  if (!currentPageData) {
    return (
      <div className="flex items-center justify-center h-[700px]">
        <div className="text-center text-muted-foreground">
          <p>画像データが見つかりません</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* ページナビゲーション */}
      {pages.length > 1 && (
        <div className="flex items-center justify-between bg-muted p-3 rounded-lg border border-border">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-card border border-border rounded hover:bg-secondary disabled:opacity-50 disabled:cursor-not-allowed text-card-foreground"
          >
            ← 前
          </button>
          <span className="text-sm text-muted-foreground">
            {currentPage} / {pages.length} ページ
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(pages.length, currentPage + 1))}
            disabled={currentPage === pages.length}
            className="px-3 py-1 bg-card border border-border rounded hover:bg-secondary disabled:opacity-50 disabled:cursor-not-allowed text-card-foreground"
          >
            次 →
          </button>
        </div>
      )}

      {/* 画像表示エリア */}
      <div className="border border-border rounded-lg overflow-hidden bg-card">
        <div className="relative">
          <canvas
            ref={canvasRef}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            className="w-full h-auto cursor-crosshair"
            style={{ maxHeight: '700px' }}
          />
          <img
            ref={imageRef}
            src={currentPageData.image_url}
            alt={`ページ ${currentPage}`}
            onLoad={handleImageLoad}
            className="hidden"
          />
        </div>
      </div>

      {/* 操作説明 */}
      <div className="text-xs text-muted-foreground text-center bg-primary/10 p-3 rounded-lg border border-primary/20">
        <p className="font-medium mb-1">📐 短径選択機能</p>
        <p>マウスでドラッグして解析したい範囲を選択してください</p>
        <p>選択した範囲の画像が解析に使用されます</p>
        <p className="mt-2 text-xs">※ 選択範囲が小さすぎる場合は画像全体が解析されます</p>
      </div>
    </div>
  )
} 