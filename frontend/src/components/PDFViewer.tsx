'use client'

import { useEffect, useState } from 'react'
import { PDFPageData } from '@/types'

interface PDFViewerProps {
  file: File
  onPDFLoad: (pages: PDFPageData[]) => void
}

export default function PDFViewer({ file, onPDFLoad }: PDFViewerProps) {
  const [pdfUrl, setPdfUrl] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (file) {
      const url = URL.createObjectURL(file)
      setPdfUrl(url)
      loadPDF(file)

      return () => {
        URL.revokeObjectURL(url)
      }
    }
  }, [file])

  const loadPDF = async (file: File) => {
    setIsLoading(true)
    try {
      console.log('PDF読み込み開始:', file.name, file.size)
      // PDFを画像に変換してページデータを作成
      const { apiClient } = await import('@/lib/api')
      const pages: PDFPageData[] = await apiClient.pdfToImages(file)
      console.log('PDF変換完了:', pages.length, 'ページ')
      console.log('ページデータ:', pages.map(p => ({ page: p.page_number, hasImage: !!p.image_url, width: p.width, height: p.height })))
      setTotalPages(pages.length)
      onPDFLoad(pages)
    } catch (error) {
      console.error('PDF読み込みエラー:', error)
      alert('PDFの読み込みに失敗しました。')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[700px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-primary mx-auto"></div>
          <p className="mt-6 text-lg text-muted-foreground">PDFを読み込み中...</p>
          <p className="mt-2 text-sm text-muted-foreground">しばらくお待ちください</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* ページナビゲーション */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between bg-muted p-3 rounded-lg border border-border">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 bg-card border border-border rounded hover:bg-secondary disabled:opacity-50 disabled:cursor-not-allowed text-card-foreground"
          >
            ← 前
          </button>
          <span className="text-sm text-muted-foreground">
            {currentPage} / {totalPages} ページ
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 bg-card border border-border rounded hover:bg-secondary disabled:opacity-50 disabled:cursor-not-allowed text-card-foreground"
          >
            次 →
          </button>
        </div>
      )}

      {/* PDF表示 */}
      <div className="border border-border rounded-lg overflow-hidden bg-card">
        <iframe
          src={`${pdfUrl}#page=${currentPage}`}
          className="w-full h-[700px]"
          title="PDF Viewer"
        />
      </div>

      {/* ページ情報 */}
      <div className="text-xs text-muted-foreground text-center">
        ファイル: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
      </div>
    </div>
  )
}
