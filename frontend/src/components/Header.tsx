'use client'

import { useAuth } from '@/lib/auth'
import { useTheme } from '@/lib/theme'

export default function Header() {
  const { user, logout } = useAuth()
  const { theme, toggleTheme } = useTheme()

  return (
    <header className="bg-card border-b border-border shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <h1 className="text-2xl font-bold text-card-foreground">
            🎭 歌舞伎台本自動成形システム
          </h1>
          <div className="flex items-center space-x-4">
            {/* テーマ切り替えボタン */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-md bg-secondary hover:bg-secondary/80 text-secondary-foreground transition-colors"
              aria-label={theme === 'dark' ? 'ライトモードに切り替え' : 'ダークモードに切り替え'}
            >
              {theme === 'dark' ? '☀️' : '🌙'}
            </button>
            
            {user && (
              <div className="flex items-center space-x-4">
                <div className="text-sm text-card-foreground">
                  ようこそ、<span className="font-medium">{user.username}</span>さん
                </div>
                <button
                  onClick={logout}
                  className="text-sm bg-secondary hover:bg-secondary/80 text-secondary-foreground px-3 py-1 rounded-md transition-colors"
                >
                  ログアウト
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
} 