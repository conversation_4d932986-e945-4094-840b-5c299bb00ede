"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth";
import LoginForm from "@/components/LoginForm";
import PDFUploader from "@/components/PDFUploader";
import ImageViewer from "@/components/ImageViewer";
import ScriptEditor from "@/components/ScriptEditor";
import { Paragraph, PDFPageData, VisionAnalysisResult } from "@/types";

export default function Home() {
  const { user, isLoading, refresh, logout } = useAuth();
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [pdfPages, setPdfPages] = useState<PDFPageData[]>([]);
  const [paragraphs, setParagraphs] = useState<Paragraph[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [analysisResult, setAnalysisResult] =
    useState<VisionAnalysisResult | null>(null);
  const [selectedImageData, setSelectedImageData] = useState<string | null>(
    null
  );
  const [currentPageData, setCurrentPageData] = useState<PDFPageData | null>(
    null
  );

  // Move useEffect to top, before any conditional returns
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        refresh().catch(() => {
          alert("認証エラーのためログイン画面に戻ります");
          logout();
        });
      }
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [refresh, logout]);

  // ローディング中の表示
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-32 h-32 mx-auto border-b-2 rounded-full animate-spin border-primary"></div>
          <p className="mt-4 text-muted-foreground">読み込み中...</p>
        </div>
      </div>
    );
  }

  // ログインしていない場合はログインフォームを表示
  if (!user) {
    return <LoginForm />;
  }

  const handleFileUpload = async (file: File) => {
    setUploadedFile(file);
    setPdfPages([]); // pdfPagesもリセット
    setParagraphs([]);
    setAnalysisResult(null);
    setSelectedImageData(null);
    setIsConverting(true);

    // ファイルアップロード時に直接画像変換を実行
    try {
      const { apiClient } = await import("@/lib/api");
      const pages: PDFPageData[] = await apiClient.pdfToImages(file);
      setPdfPages(pages);
    } catch (error) {
      console.error("PDF変換エラー:", error);
      alert("PDFの変換に失敗しました。");
    } finally {
      setIsConverting(false);
    }
  };

  const handleAnalyze = async () => {
    if (!uploadedFile || pdfPages.length === 0) return;

    setIsAnalyzing(true);
    try {
      const { apiClient } = await import("@/lib/api");

      // 現在表示されているページの画像データを取得
      const targetPage = currentPageData || pdfPages[0]; // 現在のページまたは最初のページを使用
      if (!targetPage || !targetPage.image_url) {
        throw new Error("画像データが見つかりません");
      }

      console.log("全体解析開始:", {
        pageNumber: targetPage.page_number,
        imageUrlLength: targetPage.image_url.length,
      });

      const result: VisionAnalysisResult = await apiClient.analyzeImage(
        targetPage.image_url
      );
      setAnalysisResult(result);
      setParagraphs(result.paragraphs);
    } catch (error) {
      console.error("解析エラー:", error);
      alert("解析に失敗しました。もう一度お試しください。");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleImageSelect = async (
    imageData: string,
    cropData?: { x: number; y: number; width: number; height: number }
  ) => {
    setSelectedImageData(imageData);

    // 自動的に解析を開始
    setIsAnalyzing(true);
    try {
      const { apiClient } = await import("@/lib/api");
      const result: VisionAnalysisResult = await apiClient.analyzeImage(
        imageData
      );
      setAnalysisResult(result);
      setParagraphs(result.paragraphs);
    } catch (error) {
      console.error("画像解析エラー:", error);
      alert("画像解析に失敗しました。もう一度お試しください。");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleScriptChange = (paragraphs: Paragraph[]) => {
    setParagraphs(paragraphs);
  };

  const handleGenerateWord = async () => {
    if (paragraphs.length === 0) return;

    try {
      const { apiClient } = await import("@/lib/api");
      const blob = await apiClient.generateWord(paragraphs);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `kabuki-script-${
        new Date().toISOString().split("T")[0]
      }.docx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Word生成エラー:", error);
      alert("Word文書の生成に失敗しました。");
    }
  };

  return (
    <div className="space-y-6">
      {/* アップロードセクション */}
      <div className="p-6 border rounded-lg shadow bg-card border-border">
        <h2 className="mb-4 text-xl font-semibold text-card-foreground">
          📄 PDFアップロード
        </h2>
        <PDFUploader onFileUpload={handleFileUpload} />
        {uploadedFile && (
          <div className="p-4 mt-4 border rounded-lg bg-primary/10 border-primary/20">
            <p className="text-sm text-primary">
              アップロード完了: {uploadedFile.name}
            </p>
          </div>
        )}
      </div>

      {/* 解析結果情報 */}
      {analysisResult && (
        <div className="p-4 border rounded-lg shadow bg-card border-border">
          <div className="flex items-center justify-center">
            <div className="px-4 py-2 text-sm border rounded-lg text-card-foreground bg-primary/10 border-primary/20">
              解析完了: {analysisResult.paragraphs.length}段落 | 信頼度:{" "}
              {((analysisResult.confidence || 0) * 100).toFixed(1)}% | 処理時間:{" "}
              {(analysisResult.processing_time || 0).toFixed(1)}秒
            </div>
          </div>
        </div>
      )}

      {/* メイン作業エリア */}
      {uploadedFile && (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* PDF表示エリア */}
          <div className="border rounded-lg shadow bg-card border-border">
            <div className="p-4 border-b border-border">
              <h3 className="text-lg font-semibold text-card-foreground">
                画像表示
              </h3>
            </div>
            <div className="p-4">
              {isConverting ? (
                <div className="flex items-center justify-center h-[700px]">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto border-b-4 rounded-full animate-spin border-primary"></div>
                    <p className="mt-6 text-lg text-muted-foreground">
                      PDFを画像に変換中...
                    </p>
                    <p className="mt-2 text-sm text-muted-foreground">
                      しばらくお待ちください
                    </p>
                  </div>
                </div>
              ) : pdfPages.length > 0 ? (
                <ImageViewer
                  pages={pdfPages}
                  onImageSelect={handleImageSelect}
                  onCurrentPageChange={setCurrentPageData}
                />
              ) : (
                <div className="flex items-center justify-center h-[700px]">
                  <div className="text-center text-muted-foreground">
                    <p>PDFファイルをアップロードしてください</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 編集エリア */}
          <div className="border rounded-lg shadow bg-card border-border">
            <div className="flex items-center justify-between p-4 border-b border-border">
              <h3 className="text-lg font-semibold text-card-foreground">
                台本編集
              </h3>
              <div className="space-x-2">
                <button
                  onClick={handleAnalyze}
                  disabled={isAnalyzing || pdfPages.length === 0}
                  className="px-4 py-2 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isAnalyzing ? "解析中..." : "🔍 全体解析"}
                </button>
                <button
                  onClick={handleGenerateWord}
                  disabled={paragraphs.length === 0}
                  className="px-4 py-2 text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  📄 Word生成
                </button>
              </div>
            </div>
            <div className="p-4">
              <ScriptEditor
                paragraphs={paragraphs}
                onChange={handleScriptChange}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
